@echo off
echo ========================================
echo   Démarrage Projet Mobile AquaTrack
echo ========================================
echo.
echo 🚀 Démarrage du projet mobile complet:
echo   • Backend Node.js (API)
echo   • Frontend React Native (Mobile)
echo.

REM Vérifier si Node.js est installé
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js n'est pas installé ou n'est pas dans le PATH
    echo 💡 Veuillez installer Node.js depuis https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js détecté: 
node --version

REM Vérifier si Expo CLI est installé
npx expo --version >nul 2>&1
if errorlevel 1 (
    echo ⚠️ Expo CLI non détecté, installation...
    npm install -g @expo/cli
)

echo.
echo 📡 Étape 1: Démarrage du Backend (API)
echo ========================================

REM Démarrer le serveur backend simple qui fonctionne
echo 🔄 Démarrage du serveur backend sur le port 4000...
start "Backend AquaTrack" cmd /k "cd server && node simple-working-server.js"

REM Attendre que le backend démarre
echo ⏳ Attente du démarrage du backend (5 secondes)...
timeout /t 5 /nobreak >nul

echo.
echo 📱 Étape 2: Démarrage du Frontend React Native
echo ========================================

REM Aller dans le dossier react-native
cd react-native

echo 🔄 Vérification des dépendances...
if not exist node_modules (
    echo 📦 Installation des dépendances React Native...
    npm install
)

echo.
echo 🎯 Choix du mode de démarrage:
echo.
echo 1. Mode Web (navigateur PC)
echo 2. Mode Android (émulateur/appareil)
echo 3. Mode Tunnel (pour téléphone via QR code)
echo 4. Mode Développement (tous les modes)
echo.
set /p choice="Choisissez une option (1-4): "

if "%choice%"=="1" goto web
if "%choice%"=="2" goto android
if "%choice%"=="3" goto tunnel
if "%choice%"=="4" goto dev
goto web

:web
echo.
echo 🌐 Démarrage en mode Web...
echo 📍 L'application s'ouvrira sur: http://localhost:8081
start "React Native Web" cmd /k "npx expo start --web --port 8081"
goto end

:android
echo.
echo 📱 Démarrage en mode Android...
echo 💡 Assurez-vous que l'émulateur Android est démarré
start "React Native Android" cmd /k "npx expo start --android"
goto end

:tunnel
echo.
echo 🌐 Démarrage en mode Tunnel...
echo 📱 Scannez le QR code avec l'app Expo Go sur votre téléphone
start "React Native Tunnel" cmd /k "npx expo start --tunnel"
goto end

:dev
echo.
echo 🔧 Démarrage en mode Développement...
echo 🌐 Web: http://localhost:8081
echo 📱 Scannez le QR code pour mobile
start "React Native Dev" cmd /k "npx expo start --dev-client"
goto end

:end
echo.
echo ========================================
echo   🎉 Projet Mobile Démarré !
echo ========================================
echo.
echo 📡 Backend API: http://localhost:4000
echo 📱 Frontend Mobile: Voir la fenêtre Expo
echo.
echo 🔑 Comptes de test:
echo   • Technicien: <EMAIL> / Tech123
echo   • Admin: <EMAIL> / Admin123
echo.
echo 📋 Données de test disponibles:
echo   • 2 clients avec contrats
echo   • 2 secteurs avec coordonnées GPS
echo   • Fonctionnalité de localisation complète
echo.
echo 💡 Instructions:
echo   1. Le backend API tourne sur le port 4000
echo   2. Le frontend React Native est accessible selon le mode choisi
echo   3. Utilisez les comptes de test pour vous connecter
echo   4. Testez la fonctionnalité de localisation
echo.
echo ⚠️ Pour arrêter: Fermez les fenêtres de commande ouvertes
echo.

pause
