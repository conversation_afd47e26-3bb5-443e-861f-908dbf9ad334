const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');

const app = express();
const PORT = 4000;

// Middleware
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:3002'],
  credentials: true
}));
app.use(express.json());

// Configuration de la base de données PostgreSQL "Facutration"
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facutration',
  password: '123456',
  port: 5432,
});

// Middleware de logging
app.use((req, res, next) => {
  console.log(`📥 ${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// Test de connexion à la base de données
async function testDatabaseConnection() {
  try {
    const client = await pool.connect();
    console.log('✅ Connexion à la base de données "Facutration" réussie');
    
    // Test simple pour vérifier les tables
    const clientResult = await client.query('SELECT COUNT(*) FROM client');
    console.log(`📊 ${clientResult.rows[0].count} clients trouvés dans la base`);
    
    const contractResult = await client.query('SELECT COUNT(*) FROM contract');
    console.log(`📋 ${contractResult.rows[0].count} contrats trouvés dans la base`);
    
    client.release();
    return true;
  } catch (err) {
    console.error('❌ Erreur de connexion à la base de données:', err.message);
    return false;
  }
}

// Route de test
app.get('/', (req, res) => {
  res.json({
    message: 'Serveur AquaTrack avec base de données Facutration',
    timestamp: new Date().toISOString(),
    database: 'Facutration',
    port: PORT
  });
});

// Route pour récupérer tous les clients
app.get('/api/clients', async (req, res) => {
  try {
    console.log('📥 Récupération de tous les clients depuis la base "Facutration"');

    const query = `
      SELECT
        c.idclient,
        c.nom,
        c.prenom,
        c.adresse,
        c.ville,
        c.tel,
        c.email,
        c.ids,
        s.nom as secteur_nom
      FROM client c
      LEFT JOIN secteur s ON c.ids = s.ids
      ORDER BY c.nom, c.prenom
    `;

    const result = await pool.query(query);
    
    console.log(`✅ ${result.rows.length} clients récupérés avec succès`);
    
    res.json({
      success: true,
      data: result.rows,
      count: result.rows.length,
      message: `${result.rows.length} client(s) trouvé(s) dans la base Facutration`
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des clients:', error.message);
    res.status(500).json({
      success: false,
      error: error.message,
      message: 'Erreur lors de la récupération des clients depuis la base Facutration'
    });
  }
});

// Route pour récupérer un client spécifique par ID
app.get('/api/clients/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`📥 Récupération du client ID: ${id}`);

    const query = `
      SELECT
        c.idclient,
        c.nom,
        c.prenom,
        c.adresse,
        c.ville,
        c.tel,
        c.email,
        c.ids,
        s.nom as secteur_nom
      FROM client c
      LEFT JOIN secteur s ON c.ids = s.ids
      WHERE c.idclient = $1
    `;

    const result = await pool.query(query, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Client non trouvé dans la base Facutration'
      });
    }

    console.log(`✅ Client ${id} récupéré avec succès`);
    res.json({
      success: true,
      data: result.rows[0],
      message: 'Client trouvé'
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération du client:', error.message);
    res.status(500).json({
      success: false,
      error: error.message,
      message: 'Erreur lors de la récupération du client'
    });
  }
});

// Route pour récupérer les contrats d'un client
app.get('/api/clients/:id/contracts', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`📥 Récupération des contrats pour le client ID: ${id}`);

    const query = `
      SELECT
        cont.idcontract,
        cont.codeqr,
        cont.datecontract,
        cont.marquecompteur,
        cont.numseriecompteur,
        cont.posx,
        cont.posy
      FROM contract cont
      WHERE cont.idclient = $1
      ORDER BY cont.datecontract DESC
    `;

    const result = await pool.query(query, [id]);
    
    console.log(`✅ ${result.rows.length} contrat(s) trouvé(s) pour le client ${id}`);
    
    res.json({
      success: true,
      data: result.rows,
      count: result.rows.length,
      message: `${result.rows.length} contrat(s) trouvé(s)`
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des contrats:', error.message);
    res.status(500).json({
      success: false,
      error: error.message,
      message: 'Erreur lors de la récupération des contrats'
    });
  }
});

// Route pour scanner un QR Code
app.get('/api/scan/:qrCode', async (req, res) => {
  try {
    const { qrCode } = req.params;
    console.log(`🔍 Scan du QR Code: ${qrCode}`);

    // Rechercher le contrat avec ce QR Code
    const contractQuery = `
      SELECT
        cont.idcontract,
        cont.codeqr,
        cont.datecontract,
        cont.marquecompteur,
        cont.numseriecompteur,
        cont.posx,
        cont.posy,
        cont.idclient
      FROM contract cont
      WHERE cont.codeqr = $1
    `;

    const contractResult = await pool.query(contractQuery, [qrCode]);

    if (contractResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'QR Code non trouvé dans la base de données',
        qrCode: qrCode
      });
    }

    const contract = contractResult.rows[0];

    // Récupérer les informations du client
    const clientQuery = `
      SELECT
        c.idclient,
        c.nom,
        c.prenom,
        c.adresse,
        c.ville,
        c.tel,
        c.email,
        c.ids,
        s.nom as secteur_nom
      FROM client c
      LEFT JOIN secteur s ON c.ids = s.ids
      WHERE c.idclient = $1
    `;

    const clientResult = await pool.query(clientQuery, [contract.idclient]);

    if (clientResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Client associé au contrat non trouvé'
      });
    }

    const client = clientResult.rows[0];

    console.log(`✅ QR Code ${qrCode} trouvé - Client: ${client.nom} ${client.prenom}`);

    res.json({
      success: true,
      data: {
        client: client,
        contract: contract
      },
      message: 'QR Code trouvé avec succès'
    });

  } catch (error) {
    console.error('❌ Erreur lors du scan du QR Code:', error.message);
    res.status(500).json({
      success: false,
      error: error.message,
      message: 'Erreur lors du scan du QR Code'
    });
  }
});

// Route pour récupérer les secteurs avec coordonnées
app.get('/api/secteurs/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`📥 Récupération du secteur ID: ${id}`);

    const query = `
      SELECT ids, nom, latitude, longitude
      FROM secteur
      WHERE ids = $1
    `;

    const result = await pool.query(query, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Secteur non trouvé'
      });
    }

    console.log(`✅ Secteur ${id} récupéré avec succès: ${result.rows[0].nom}`);
    console.log(`📍 Coordonnées: ${result.rows[0].latitude}, ${result.rows[0].longitude}`);

    res.json({
      success: true,
      data: result.rows[0],
      message: 'Secteur trouvé'
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération du secteur:', error.message);
    res.status(500).json({
      success: false,
      error: error.message,
      message: 'Erreur lors de la récupération du secteur'
    });
  }
});

// Démarrage du serveur
async function startServer() {
  console.log('🔄 Démarrage du serveur AquaTrack avec base de données...');
  
  // Tester la connexion à la base de données
  const dbConnected = await testDatabaseConnection();
  if (!dbConnected) {
    console.error('❌ Impossible de se connecter à la base de données "Facutration"');
    console.error('💡 Vérifiez que PostgreSQL est démarré et que la base existe');
    process.exit(1);
  }

  app.listen(PORT, () => {
    console.log(`\n🚀 Serveur AquaTrack démarré sur http://localhost:${PORT}`);
    console.log('📊 Base de données: Facutration (PostgreSQL)');
    console.log('📡 Routes disponibles:');
    console.log('  - GET  / (test)');
    console.log('  - GET  /api/clients (tous les clients)');
    console.log('  - GET  /api/clients/:id (client spécifique)');
    console.log('  - GET  /api/clients/:id/contracts (contrats d\'un client)');
    console.log('  - GET  /api/scan/:qrCode (scanner QR Code)');
    console.log('  - GET  /api/secteurs/:id (secteur par ID)');
    console.log('\n✅ Serveur prêt à recevoir les requêtes du frontend');
  });
}

// Gestion des erreurs non capturées
process.on('uncaughtException', (err) => {
  console.error('❌ Erreur non capturée:', err);
});

process.on('unhandledRejection', (err) => {
  console.error('❌ Promesse rejetée non gérée:', err);
});

// Démarrer le serveur
startServer();
