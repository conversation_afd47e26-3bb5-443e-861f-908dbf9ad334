@echo off
echo ========================================
echo   AquaTrack Mobile - Mode Android
echo ========================================
echo.

REM Démarrer le backend
echo 🚀 Démarrage du backend...
start "Backend AquaTrack" cmd /k "cd server && node simple-working-server.js"

REM Attendre 3 secondes
timeout /t 3 /nobreak >nul

REM Démarrer React Native en mode Android
echo 📱 Démarrage React Native pour Android...
echo 💡 Assurez-vous que l'émulateur Android est démarré
cd react-native
start "React Native Android" cmd /k "npx expo start --android"

echo.
echo ✅ Projet démarré !
echo.
echo 📡 Backend: http://localhost:4000
echo 📱 Frontend: Mode Android
echo.
echo 🔑 Comptes de test:
echo   • <EMAIL> / Tech123
echo   • <EMAIL> / Admin123
echo.
echo 💡 Instructions:
echo   1. L'émulateur Android va s'ouvrir automatiquement
echo   2. L'application se chargera dans l'émulateur
echo   3. Utilisez les comptes de test pour vous connecter
echo.

pause
