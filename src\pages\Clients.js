import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  Alert,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import { useNavigate } from 'react-router-dom';
// Icônes remplacées par des émojis pour compatibilité React Web

const Clients = ({ navigation, onLogout }) => {
  const navigate = useNavigate();
  const [clients, setClients] = useState([]);
  const [filteredClients, setFilteredClients] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchText, setSearchText] = useState('');

  useEffect(() => {
    fetchClients();
  }, []);

  useEffect(() => {
    // Filtrer les clients selon le texte de recherche
    if (searchText.trim() === '') {
      setFilteredClients(clients);
    } else {
      const filtered = clients.filter(client =>
        client.nom.toLowerCase().includes(searchText.toLowerCase()) ||
        client.prenom.toLowerCase().includes(searchText.toLowerCase()) ||
        client.email.toLowerCase().includes(searchText.toLowerCase()) ||
        client.ville.toLowerCase().includes(searchText.toLowerCase())
      );
      setFilteredClients(filtered);
    }
  }, [searchText, clients]);

  const fetchClients = async () => {
    try {
      console.log('🔍 Récupération des clients...');
      const response = await fetch('http://localhost:4000/api/clients');

      if (response.ok) {
        const data = await response.json();
        console.log('📊 Données reçues:', data);

        // Vérifier si la réponse contient des clients
        if (data.success && data.clients) {
          setClients(data.clients);
          setFilteredClients(data.clients);
          console.log('✅ Clients chargés:', data.clients.length);
        } else if (data.length) {
          // Si les données sont directement un tableau
          setClients(data);
          setFilteredClients(data);
          console.log('✅ Clients chargés (format direct):', data.length);
        } else {
          setClients([]);
          setFilteredClients([]);
          console.log('⚠️ Aucun client trouvé');
        }
      } else {
        console.error('❌ Erreur HTTP:', response.status);
        setError('Erreur lors du chargement des clients');
      }
    } catch (err) {
      console.error('❌ Erreur de connexion:', err);
      setError('Erreur de connexion au serveur');
    } finally {
      setLoading(false);
    }
  };



  const handleConsommationPress = (client) => {
    console.log('Redirection vers consommation pour:', client.nom, client.prenom);
    // TODO: Navigation vers la page de consommation
    Alert.alert(
      'Consommation',
      `Redirection vers la saisie de consommation pour ${client.nom} ${client.prenom}`,
      [{ text: 'OK' }]
    );
  };

  const handleLocationPress = async (client) => {
    console.log('�️ Localisation demandée pour:', client.nom, client.prenom, 'ID:', client.idclient);

    // Redirection directe vers Google Maps
    await fetchClientLocationAndRedirect(client);
  };

  const fetchClientLocationAndRedirect = async (client) => {
    try {
      console.log('🔍 Récupération contrat et secteur pour redirection Google Maps - Client ID:', client.idclient);

      // Récupérer le contrat avec les informations du secteur
      const contractUrl = `http://localhost:4000/api/client-contract/${client.idclient}`;
      console.log('📡 URL contrat+secteur appelée:', contractUrl);

      const contractResponse = await fetch(contractUrl);
      console.log('📡 Réponse statut contrat+secteur:', contractResponse.status);

      if (!contractResponse.ok) {
        if (contractResponse.status === 404) {
          Alert.alert(
            '❌ Aucun Contrat',
            `Aucun contrat trouvé pour ${client.nom} ${client.prenom}.\n\nCe client n'a pas encore de contrat enregistré dans le système.\n\nImpossible d'afficher la localisation.`,
            [{ text: 'OK' }]
          );
        } else {
          Alert.alert('Erreur', `Erreur de connexion: ${contractResponse.status}`);
        }
        return;
      }

      const contractData = await contractResponse.json();
      console.log('📋 Données contrat+secteur reçues:', contractData);

      if (!contractData.success || !contractData.contract) {
        Alert.alert(
          '❌ Aucun Contrat',
          `Aucun contrat trouvé pour ${client.nom} ${client.prenom}.\n\nImpossible d'afficher la localisation.`,
          [{ text: 'OK' }]
        );
        return;
      }

      const contract = contractData.contract;
      console.log('✅ Contrat avec secteur trouvé:', contract);

      // Redirection directe vers Google Maps
      redirectToGoogleMaps(client, contract);

    } catch (error) {
      console.error('❌ Erreur lors de la récupération de la localisation:', error);
      Alert.alert('Erreur', `Erreur de connexion: ${error.message}`);
    }
  };

  const redirectToGoogleMaps = (client, contract) => {
    console.log('🗺️ Redirection Google Maps pour:', client.nom, client.prenom);
    console.log('📍 Données contrat:', contract);

    // Étape 1: Vérifier et ouvrir le secteur d'abord
    if (contract.secteur_latitude && contract.secteur_longitude) {
      console.log('📍 Ouverture du secteur:', contract.secteur_nom);
      const secteurUrl = `https://www.google.com/maps?q=${contract.secteur_latitude},${contract.secteur_longitude}&z=15`;

      // Ouvrir le secteur immédiatement
      window.open(secteurUrl, '_blank');

      // Attendre 3 secondes puis ouvrir la position exacte si disponible
      setTimeout(() => {
        if (contract.posx && contract.posy) {
          console.log('📍 Ouverture de la position exacte du compteur');
          const exactUrl = `https://www.google.com/maps?q=${contract.posx},${contract.posy}&z=18`;
          window.open(exactUrl, '_blank');

          // Afficher un message informatif
          Alert.alert(
            '🗺️ Localisation Ouverte',
            `Localisation de ${client.nom} ${client.prenom} :\n\n✅ Secteur: ${contract.secteur_nom || 'Non défini'}\n✅ Position exacte du compteur\n\nDeux onglets Google Maps ont été ouverts :\n1. Vue du secteur (zone générale)\n2. Position exacte du compteur`,
            [{ text: 'OK' }]
          );
        } else {
          // Seulement le secteur disponible
          Alert.alert(
            '🗺️ Secteur Ouvert',
            `Secteur de ${client.nom} ${client.prenom} :\n\n✅ ${contract.secteur_nom || 'Non défini'}\n\n⚠️ Position exacte du compteur non disponible\n\nUn onglet Google Maps a été ouvert avec la vue du secteur.`,
            [{ text: 'OK' }]
          );
        }
      }, 3000); // Attendre 3 secondes

    } else if (contract.posx && contract.posy) {
      // Seulement la position exacte disponible
      console.log('📍 Ouverture de la position exacte seulement');
      const exactUrl = `https://www.google.com/maps?q=${contract.posx},${contract.posy}&z=18`;
      window.open(exactUrl, '_blank');

      Alert.alert(
        '🗺️ Position Exacte Ouverte',
        `Position du compteur de ${client.nom} ${client.prenom} :\n\n✅ Coordonnées exactes\n⚠️ Informations du secteur non disponibles\n\nUn onglet Google Maps a été ouvert avec la position exacte du compteur.`,
        [{ text: 'OK' }]
      );

    } else {
      // Aucune coordonnée disponible
      Alert.alert(
        '❌ Localisation Non Disponible',
        `Désolé, aucune coordonnée n'est disponible pour ${client.nom} ${client.prenom}.\n\n❌ Secteur: coordonnées manquantes\n❌ Compteur: coordonnées manquantes\n\nVeuillez contacter l'administrateur pour mettre à jour ces informations.`,
        [{ text: 'OK' }]
      );
    }
  };







  const renderClientItem = ({ item }) => (
    <View style={styles.clientCard}>
      <View style={styles.clientInfo}>
        <Text style={styles.clientName}>{item.nom} {item.prenom}</Text>
        <Text style={styles.clientDetail}>📍 {item.adresse}</Text>
        <Text style={styles.clientDetail}>🏙️ {item.ville}</Text>
        <Text style={styles.clientDetail}>📞 {item.tel}</Text>
        <Text style={styles.clientDetail}>📧 {item.email}</Text>
        {item.statut && (
          <View style={styles.statusContainer}>
            <Text style={[styles.statusText,
              item.statut === 'Actif' ? styles.statusActive :
              item.statut === 'Inactif' ? styles.statusInactive : styles.statusSuspended
            ]}>
              {item.statut}
            </Text>
          </View>
        )}

        {/* Boutons d'action */}
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleConsommationPress(item)}
          >
            <Text style={styles.buttonIcon}>💧</Text>
            <Text style={styles.buttonText}>Consommation</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, styles.locationButton]}
            onPress={() => {
              console.log('🔴 BOUTON LOCALISATION CLIQUÉ pour:', item.nom, item.prenom);
              handleLocationPress(item);
            }}
          >
            <Text style={styles.buttonIcon}>📍</Text>
            <Text style={styles.buttonText}>Localisation</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#6366f1" />
          <Text style={styles.loadingText}>Chargement des clients...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => navigate('/dashboard')}>
          <Text style={styles.backIcon}>←</Text>
          <Text style={styles.backText}>Retour</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>👥 Les Clients</Text>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Text style={styles.searchIcon}>🔍</Text>
        <TextInput
          style={styles.searchInput}
          placeholder="Rechercher un client..."
          value={searchText}
          onChangeText={setSearchText}
          placeholderTextColor="#999"
        />
      </View>

      {/* Error Message */}
      {error ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>❌ {error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={fetchClients}>
            <Text style={styles.retryText}>Réessayer</Text>
          </TouchableOpacity>
        </View>
      ) : null}

      {/* Clients List */}
      {filteredClients.length > 0 ? (
        <FlatList
          data={filteredClients}
          renderItem={renderClientItem}
          keyExtractor={(item) => item.idclient.toString()}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
        />
      ) : (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyIcon}>👥</Text>
          <Text style={styles.emptyText}>
            {searchText ? 'Aucun client trouvé pour cette recherche' : 'Aucun client trouvé dans la base de données'}
          </Text>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    color:'#000',
    padding: 20,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 20,
  },
  backIcon: {
    color: '#000',
    fontSize: 20,
    marginRight: 8,
  },
  backText: {
    color: '#000',
    fontSize: 18, // Augmenté de 16 à 18
    fontWeight: '500',
  },
  headerTitle: {
    fontSize: 26, // Augmenté de 24 à 26
    fontWeight: 'bold',
    color: '#000',
    flex: 1,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    margin: 20,
    marginHorizontal: 100, // Marges encore plus grandes pour réduire la largeur
    maxWidth: 250, // Largeur maximale encore plus réduite
    alignSelf: 'center', // Centrer la barre de recherche
    backgroundColor: 'white',
    borderRadius: 10,
    paddingHorizontal: 12,
    paddingVertical: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.08,
    shadowRadius: 3,
    elevation: 2,
  },
  searchIcon: {
    fontSize: 16,
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    fontSize: 18, // Taille augmentée pour la recherche
    color: '#1f2937',
  },
  listContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  clientCard: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    marginHorizontal: 80, // Marges encore plus grandes pour réduire davantage la largeur
    minHeight: 220, // Hauteur augmentée pour accommoder les boutons
    width: '60%', // Largeur encore plus réduite de 70% à 60%
    alignSelf: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.08,
    shadowRadius: 3,
    elevation: 2,
  },
  clientInfo: {
    flex: 1,
    justifyContent: 'flex-start',
  },
  clientName: {
    fontSize: 20, // Encore augmenté de 18 à 20
    fontWeight: 'bold',
    color: '#000',
    marginBottom: 8,
  },
  clientDetail: {
    fontSize: 16, // Encore augmenté de 14 à 16
    color: '#000',
    marginBottom: 4, // Légèrement augmenté pour plus d'espace
    lineHeight: 20, // Augmenté pour meilleure lisibilité
  },
  statusContainer: {
    marginTop: 6,
    alignSelf: 'flex-start',
  },
  statusText: {
    fontSize: 10,
    fontWeight: 'bold',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    textAlign: 'center',
    minWidth: 20,
    height: 20,
    lineHeight: 16,
  },
  statusActive: {
    backgroundColor: '#d1fae5',
    color: '#065f46',
  },
  statusInactive: {
    backgroundColor: '#fee2e2',
    color: '#991b1b',
  },
  statusSuspended: {
    backgroundColor: '#fef3c7',
    color: '#92400e',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#000',
  },
  errorContainer: {
    margin: 20,
    padding: 20,
    backgroundColor: '#fef2f2',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#fecaca',
  },
  errorText: {
    color: '#ef4444',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 10,
  },
  retryButton: {
    backgroundColor: '#6366f1',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
    alignSelf: 'center',
  },
  retryText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 15,
    gap: 15,
  },
  actionButton: {
    backgroundColor: '#6366f1',
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 16,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    minWidth: 100,
    maxWidth: 120,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  locationButton: {
    backgroundColor: '#10b981',
  },
  buttonIcon: {
    fontSize: 16,
    marginRight: 6,
  },
  buttonText: {
    color: 'white',
    fontSize: 11,
    fontWeight: '600',
    textAlign: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },

  emptyIcon: {
    fontSize: 64,
    color: '#ccc',
  },
  emptyText: {
    fontSize: 16,
    color: '#000',
    textAlign: 'center',
    marginTop: 20,
  },
});
export default Clients;
