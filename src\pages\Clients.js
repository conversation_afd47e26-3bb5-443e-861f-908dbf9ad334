import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  Alert,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import { useNavigate } from 'react-router-dom';
// Icônes remplacées par des émojis pour compatibilité React Web

const Clients = ({ navigation, onLogout }) => {
  const navigate = useNavigate();
  const [clients, setClients] = useState([]);
  const [filteredClients, setFilteredClients] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchText, setSearchText] = useState('');

  useEffect(() => {
    fetchClients();
  }, []);

  useEffect(() => {
    // Filtrer les clients selon le texte de recherche
    if (searchText.trim() === '') {
      setFilteredClients(clients);
    } else {
      const filtered = clients.filter(client =>
        client.nom.toLowerCase().includes(searchText.toLowerCase()) ||
        client.prenom.toLowerCase().includes(searchText.toLowerCase()) ||
        client.email.toLowerCase().includes(searchText.toLowerCase()) ||
        client.ville.toLowerCase().includes(searchText.toLowerCase())
      );
      setFilteredClients(filtered);
    }
  }, [searchText, clients]);

  const fetchClients = async () => {
    try {
      console.log('🔍 Récupération des clients...');
      const response = await fetch('http://localhost:4000/api/clients');

      if (response.ok) {
        const data = await response.json();
        console.log('📊 Données reçues:', data);

        // Vérifier si la réponse contient des clients
        if (data.success && data.clients) {
          setClients(data.clients);
          setFilteredClients(data.clients);
          console.log('✅ Clients chargés:', data.clients.length);
        } else if (data.length) {
          // Si les données sont directement un tableau
          setClients(data);
          setFilteredClients(data);
          console.log('✅ Clients chargés (format direct):', data.length);
        } else {
          setClients([]);
          setFilteredClients([]);
          console.log('⚠️ Aucun client trouvé');
        }
      } else {
        console.error('❌ Erreur HTTP:', response.status);
        setError('Erreur lors du chargement des clients');
      }
    } catch (err) {
      console.error('❌ Erreur de connexion:', err);
      setError('Erreur de connexion au serveur');
    } finally {
      setLoading(false);
    }
  };



  const handleConsommationPress = (client) => {
    console.log('Redirection vers consommation pour:', client.nom, client.prenom);
    // TODO: Navigation vers la page de consommation
    Alert.alert(
      'Consommation',
      `Redirection vers la saisie de consommation pour ${client.nom} ${client.prenom}`,
      [{ text: 'OK' }]
    );
  };

  const handleLocationPress = async (client) => {
    console.log('🔍 DEBUT handleLocationPress pour:', client.nom, client.prenom, 'ID:', client.idclient);

    // Test simple d'abord
    Alert.alert(
      'Test Localisation',
      `Bouton localisation cliqué pour ${client.nom} ${client.prenom}`,
      [
        { text: 'Annuler', style: 'cancel' },
        {
          text: 'Continuer',
          onPress: () => fetchClientContract(client)
        }
      ]
    );
  };

  const fetchClientContract = async (client) => {
    try {
      console.log('🔍 Récupération contrat et secteur pour client ID:', client.idclient);

      // Utiliser la route spécialisée qui récupère contrat + secteur en une seule fois
      const contractUrl = `http://localhost:4000/api/client-contract/${client.idclient}`;
      console.log('📡 URL contrat+secteur appelée:', contractUrl);

      const contractResponse = await fetch(contractUrl);
      console.log('📡 Réponse statut contrat+secteur:', contractResponse.status);

      if (!contractResponse.ok) {
        if (contractResponse.status === 404) {
          Alert.alert(
            'Information',
            `Aucun contrat trouvé pour ${client.nom} ${client.prenom}.\n\nCe client n'a pas encore de contrat enregistré dans le système.`,
            [{ text: 'OK' }]
          );
        } else {
          console.error('❌ Erreur HTTP contrat:', contractResponse.status);
          Alert.alert('Erreur', `Erreur HTTP: ${contractResponse.status}`);
        }
        return;
      }

      const contractData = await contractResponse.json();
      console.log('📋 Données contrat+secteur reçues:', contractData);

      if (!contractData.success || !contractData.contract) {
        Alert.alert(
          'Information',
          `Aucun contrat trouvé pour ${client.nom} ${client.prenom}.\n\nCe client n'a pas encore de contrat enregistré dans le système.`,
          [{ text: 'OK' }]
        );
        return;
      }

      const contract = contractData.contract;
      console.log('✅ Contrat avec secteur trouvé:', contract);

      // Afficher directement le menu de localisation avec toutes les données
      showLocationMenu(client, contract);

    } catch (error) {
      console.error('❌ Erreur lors de la récupération de la localisation:', error);
      Alert.alert('Erreur', `Erreur de connexion: ${error.message}`);
    }
  };

  const showLocationMenu = (client, contract) => {
    console.log('📍 Affichage du menu de localisation pour:', client.nom, client.prenom);
    console.log('📋 Données contrat avec secteur:', contract);

    // Première étape : Afficher directement le secteur
    showSecteurLocation(client, contract);
  };

  const showSecteurLocation = (client, contract) => {
    console.log('🗺️ Affichage du secteur pour:', client.nom, client.prenom);
    console.log('📍 Coordonnées secteur:', contract.secteur_latitude, contract.secteur_longitude);

    if (contract.secteur_latitude && contract.secteur_longitude) {
      const secteurUrl = `https://www.google.com/maps?q=${contract.secteur_latitude},${contract.secteur_longitude}&z=15`;

      Alert.alert(
        '📍 Étape 1: Secteur - ' + (contract.secteur_nom || 'Non défini'),
        `Client: ${client.nom} ${client.prenom}\n\nLocalisation du secteur:\n• Nom: ${contract.secteur_nom || 'Non défini'}\n• Latitude: ${contract.secteur_latitude}\n• Longitude: ${contract.secteur_longitude}\n\nCeci est la zone générale où se trouve le client.`,
        [
          { text: 'Annuler', style: 'cancel' },
          {
            text: 'Voir Secteur sur Maps',
            onPress: () => {
              console.log('🗺️ Ouverture de Google Maps pour le secteur');
              window.open(secteurUrl, '_blank');
              // Après avoir ouvert le secteur, proposer l'adresse exacte
              setTimeout(() => {
                showExactLocationOption(client, contract);
              }, 1500);
            }
          },
          {
            text: 'Passer à l\'Adresse Exacte',
            onPress: () => showExactLocation(client, contract)
          }
        ]
      );
    } else {
      Alert.alert(
        '⚠️ Secteur Non Disponible',
        `Client: ${client.nom} ${client.prenom}\n\nSecteur: ${contract.secteur_nom || 'Non défini'}\n\nLes coordonnées du secteur ne sont pas disponibles dans la base de données.\n\nVoulez-vous voir directement l'adresse exacte du compteur ?`,
        [
          { text: 'Annuler', style: 'cancel' },
          {
            text: 'Voir Adresse Exacte',
            onPress: () => showExactLocation(client, contract)
          }
        ]
      );
    }
  };

  const showExactLocationOption = (client, contract) => {
    Alert.alert(
      '🏠 Étape 2: Adresse Exacte',
      `Secteur affiché avec succès !\n\nVoulez-vous maintenant voir l'adresse exacte du compteur de ${client.nom} ${client.prenom} ?\n\nCeci vous montrera la position précise du compteur d'eau.`,
      [
        { text: 'Non, Terminé', style: 'cancel' },
        {
          text: 'Voir Adresse Exacte',
          onPress: () => showExactLocation(client, contract)
        }
      ]
    );
  };

  const showExactLocation = (client, contract) => {
    console.log('🏠 Affichage de l\'adresse exacte pour:', client.nom, client.prenom);
    console.log('📍 Coordonnées exactes:', contract.posx, contract.posy);

    if (contract.posx && contract.posy) {
      const exactUrl = `https://www.google.com/maps?q=${contract.posx},${contract.posy}&z=18`;

      Alert.alert(
        '🏠 Étape 2: Position Exacte du Compteur',
        `Client: ${client.nom} ${client.prenom}\n\n📋 Informations du contrat:\n• ID Contrat: ${contract.idcontract}\n• Code QR: ${contract.codeqr || 'Non défini'}\n• Date: ${contract.datecontract ? new Date(contract.datecontract).toLocaleDateString() : 'Non définie'}\n\n🔧 Informations du compteur:\n• Marque: ${contract.marquecompteur || 'Non définie'}\n• N° Série: ${contract.numseriecompteur || 'Non défini'}\n\n📍 Position exacte:\n• Latitude: ${contract.posx}\n• Longitude: ${contract.posy}\n\nCette position correspond à l'emplacement exact du compteur d'eau du client.`,
        [
          { text: 'Fermer', style: 'cancel' },
          {
            text: 'Ouvrir dans Google Maps',
            onPress: () => {
              console.log('🗺️ Ouverture de Google Maps pour l\'adresse exacte');
              window.open(exactUrl, '_blank');
            }
          }
        ]
      );
    } else {
      Alert.alert(
        '⚠️ Position Exacte Non Disponible',
        `Client: ${client.nom} ${client.prenom}\n\n📋 Informations du contrat:\n• ID Contrat: ${contract.idcontract}\n• Code QR: ${contract.codeqr || 'Non défini'}\n\n⚠️ Les coordonnées exactes du compteur (posX, posY) ne sont pas encore enregistrées dans la table Contract.\n\nVeuillez contacter l'administrateur pour mettre à jour ces informations dans la base de données.`,
        [{ text: 'OK' }]
      );
    }
  };

  const renderClientItem = ({ item }) => (
    <View style={styles.clientCard}>
      <View style={styles.clientInfo}>
        <Text style={styles.clientName}>{item.nom} {item.prenom}</Text>
        <Text style={styles.clientDetail}>📍 {item.adresse}</Text>
        <Text style={styles.clientDetail}>🏙️ {item.ville}</Text>
        <Text style={styles.clientDetail}>📞 {item.tel}</Text>
        <Text style={styles.clientDetail}>📧 {item.email}</Text>
        {item.statut && (
          <View style={styles.statusContainer}>
            <Text style={[styles.statusText,
              item.statut === 'Actif' ? styles.statusActive :
              item.statut === 'Inactif' ? styles.statusInactive : styles.statusSuspended
            ]}>
              {item.statut}
            </Text>
          </View>
        )}

        {/* Boutons d'action */}
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleConsommationPress(item)}
          >
            <Text style={styles.buttonIcon}>💧</Text>
            <Text style={styles.buttonText}>Consommation</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, styles.locationButton]}
            onPress={() => {
              console.log('🔴 BOUTON LOCALISATION CLIQUÉ pour:', item.nom, item.prenom);
              handleLocationPress(item);
            }}
          >
            <Text style={styles.buttonIcon}>📍</Text>
            <Text style={styles.buttonText}>Localisation</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#6366f1" />
          <Text style={styles.loadingText}>Chargement des clients...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => navigate('/dashboard')}>
          <Text style={styles.backIcon}>←</Text>
          <Text style={styles.backText}>Retour</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>👥 Les Clients</Text>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Text style={styles.searchIcon}>🔍</Text>
        <TextInput
          style={styles.searchInput}
          placeholder="Rechercher un client..."
          value={searchText}
          onChangeText={setSearchText}
          placeholderTextColor="#999"
        />
      </View>

      {/* Error Message */}
      {error ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>❌ {error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={fetchClients}>
            <Text style={styles.retryText}>Réessayer</Text>
          </TouchableOpacity>
        </View>
      ) : null}

      {/* Clients List */}
      {filteredClients.length > 0 ? (
        <FlatList
          data={filteredClients}
          renderItem={renderClientItem}
          keyExtractor={(item) => item.idclient.toString()}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
        />
      ) : (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyIcon}>👥</Text>
          <Text style={styles.emptyText}>
            {searchText ? 'Aucun client trouvé pour cette recherche' : 'Aucun client trouvé dans la base de données'}
          </Text>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    color:'#000',
    padding: 20,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 20,
  },
  backIcon: {
    color: '#000',
    fontSize: 20,
    marginRight: 8,
  },
  backText: {
    color: '#000',
    fontSize: 18, // Augmenté de 16 à 18
    fontWeight: '500',
  },
  headerTitle: {
    fontSize: 26, // Augmenté de 24 à 26
    fontWeight: 'bold',
    color: '#000',
    flex: 1,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    margin: 20,
    marginHorizontal: 100, // Marges encore plus grandes pour réduire la largeur
    maxWidth: 250, // Largeur maximale encore plus réduite
    alignSelf: 'center', // Centrer la barre de recherche
    backgroundColor: 'white',
    borderRadius: 10,
    paddingHorizontal: 12,
    paddingVertical: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.08,
    shadowRadius: 3,
    elevation: 2,
  },
  searchIcon: {
    fontSize: 16,
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    fontSize: 18, // Taille augmentée pour la recherche
    color: '#1f2937',
  },
  listContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  clientCard: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    marginHorizontal: 80, // Marges encore plus grandes pour réduire davantage la largeur
    minHeight: 220, // Hauteur augmentée pour accommoder les boutons
    width: '60%', // Largeur encore plus réduite de 70% à 60%
    alignSelf: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.08,
    shadowRadius: 3,
    elevation: 2,
  },
  clientInfo: {
    flex: 1,
    justifyContent: 'flex-start',
  },
  clientName: {
    fontSize: 20, // Encore augmenté de 18 à 20
    fontWeight: 'bold',
    color: '#000',
    marginBottom: 8,
  },
  clientDetail: {
    fontSize: 16, // Encore augmenté de 14 à 16
    color: '#000',
    marginBottom: 4, // Légèrement augmenté pour plus d'espace
    lineHeight: 20, // Augmenté pour meilleure lisibilité
  },
  statusContainer: {
    marginTop: 6,
    alignSelf: 'flex-start',
  },
  statusText: {
    fontSize: 10,
    fontWeight: 'bold',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    textAlign: 'center',
    minWidth: 20,
    height: 20,
    lineHeight: 16,
  },
  statusActive: {
    backgroundColor: '#d1fae5',
    color: '#065f46',
  },
  statusInactive: {
    backgroundColor: '#fee2e2',
    color: '#991b1b',
  },
  statusSuspended: {
    backgroundColor: '#fef3c7',
    color: '#92400e',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#000',
  },
  errorContainer: {
    margin: 20,
    padding: 20,
    backgroundColor: '#fef2f2',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#fecaca',
  },
  errorText: {
    color: '#ef4444',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 10,
  },
  retryButton: {
    backgroundColor: '#6366f1',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
    alignSelf: 'center',
  },
  retryText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 15,
    gap: 15,
  },
  actionButton: {
    backgroundColor: '#6366f1',
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 16,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    minWidth: 100,
    maxWidth: 120,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  locationButton: {
    backgroundColor: '#10b981',
  },
  buttonIcon: {
    fontSize: 16,
    marginRight: 6,
  },
  buttonText: {
    color: 'white',
    fontSize: 11,
    fontWeight: '600',
    textAlign: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },

  emptyIcon: {
    fontSize: 64,
    color: '#ccc',
  },
  emptyText: {
    fontSize: 16,
    color: '#000',
    textAlign: 'center',
    marginTop: 20,
  },
});
export default Clients;
