import React, { useState, useEffect } from 'react';
import '../TechnicianDashboard.css';

const MapPage = ({ onBack }) => {
  const [clients, setClients] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchClients();
  }, []);

  const fetchClients = async () => {
    try {
      setLoading(true);
      console.log('📥 Récupération des clients depuis la base de données Facutration...');

      const response = await fetch('http://localhost:3007/api/clients');
      const data = await response.json();

      if (data.success && data.data) {
        console.log(`✅ ${data.data.length} clients récupérés depuis la table Client`);
        setClients(data.data);
      } else {
        console.warn('⚠️ Aucun client trouvé dans la réponse');
        setClients([]);
      }
    } catch (error) {
      console.error('❌ Erreur lors du chargement des clients:', error);
      setClients([]);
    } finally {
      setLoading(false);
    }
  };

  const openGoogleMaps = async (client) => {
    try {
      console.log(`🗺️ Localisation demandée pour le client: ${client.nom} ${client.prenom}`);

      // Étape 1: Récupérer les informations du secteur
      const secteurResponse = await fetch(`http://localhost:4000/api/secteurs/${client.secteur_id}`);
      const secteurData = await secteurResponse.json();

      let secteurNom = client.secteur_nom || 'Secteur non défini';
      if (secteurData.success && secteurData.data) {
        secteurNom = secteurData.data.nom;
      }

      // Étape 2: Récupérer le contrat du client avec les coordonnées posX et posY
      const contractResponse = await fetch(`http://localhost:4000/api/clients/${client.idclient}/contracts`);
      const contractData = await contractResponse.json();

      console.log(`📍 Secteur: ${secteurNom}`);
      console.log(`📋 Contrat:`, contractData);

      // Vérifier si le client a un contrat avec des coordonnées
      if (contractData.success && contractData.data && contractData.data.length > 0) {
        const contract = contractData.data[0]; // Prendre le premier contrat

        if (contract.posx && contract.posy) {
          // Utiliser les coordonnées précises du contrat
          const latitude = parseFloat(contract.posy);
          const longitude = parseFloat(contract.posx);

          if (!isNaN(latitude) && !isNaN(longitude)) {
            // Afficher d'abord les informations du secteur
            const confirmMessage = `🏘️ Secteur: ${secteurNom}\n📍 Client: ${client.nom} ${client.prenom}\n🏠 Coordonnées précises trouvées dans le contrat\n\nOuvrir Google Maps avec la position exacte de la maison ?`;

            if (window.confirm(confirmMessage)) {
              const googleMapsUrl = `https://www.google.com/maps?q=${latitude},${longitude}&z=18`;
              window.open(googleMapsUrl, '_blank');
              console.log(`✅ Google Maps ouvert avec coordonnées précises: ${latitude}, ${longitude}`);
            }
            return;
          }
        }
      }

      // Si pas de coordonnées précises, utiliser l'adresse avec information du secteur
      const confirmMessage = `🏘️ Secteur: ${secteurNom}\n📍 Client: ${client.nom} ${client.prenom}\n⚠️ Pas de coordonnées précises dans le contrat\n\nRechercher par adresse sur Google Maps ?`;

      if (window.confirm(confirmMessage)) {
        const searchQuery = encodeURIComponent(`${client.adresse}, ${client.ville || ''}`);
        const googleMapsUrl = `https://www.google.com/maps/search/${searchQuery}`;
        window.open(googleMapsUrl, '_blank');
        console.log(`✅ Google Maps ouvert avec recherche par adresse: ${client.adresse}`);
      }

    } catch (error) {
      console.error('❌ Erreur lors de la localisation:', error);
      alert('❌ Erreur lors de la récupération des informations de localisation');
    }
  };



  if (loading) {
    return (
      <div className="tech-mobile-content">
        <div className="tech-mobile-card">
          <div className="tech-mobile-card-header">
            <button onClick={onBack} className="tech-mobile-back-btn">
              ← Retour
            </button>
            <h1 className="tech-mobile-card-title">Localisation Clients</h1>
          </div>
          <div className="tech-mobile-loading">
            <div className="tech-mobile-spinner"></div>
            <p>Chargement de la carte...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="tech-mobile-content">
      <div className="tech-mobile-card">
        <div className="tech-mobile-card-header">
          <button onClick={onBack} className="tech-mobile-back-btn">
            ← Retour
          </button>
          <div>
            <h1 className="tech-mobile-card-title">Localisation Clients</h1>
            <p className="tech-mobile-card-subtitle">
              {clients.length} client(s) géolocalisé(s)
            </p>
          </div>
        </div>
      </div>

      {/* Actions rapides */}
      <div className="tech-mobile-card">
        <div className="tech-mobile-card-header">
          <h2 className="tech-mobile-card-title">Actions Rapides</h2>
        </div>
        <div className="tech-mobile-intervention-actions" style={{ padding: '20px' }}>

          <button
            className="tech-mobile-action-btn start"
            onClick={() => {
              if (navigator.geolocation) {
                console.log('🔍 Demande de géolocalisation...');
                navigator.geolocation.getCurrentPosition(
                  (position) => {
                    const { latitude, longitude } = position.coords;
                    console.log(`📍 Position trouvée: ${latitude}, ${longitude}`);
                    const url = `https://www.google.com/maps?q=${latitude},${longitude}&z=15`;
                    window.open(url, '_blank');
                    console.log('🗺️ Google Maps ouvert avec votre position');
                  },
                  (error) => {
                    console.error('❌ Erreur de géolocalisation:', error);
                    switch(error.code) {
                      case error.PERMISSION_DENIED:
                        alert('❌ Accès à la géolocalisation refusé. Veuillez autoriser l\'accès à votre position.');
                        break;
                      case error.POSITION_UNAVAILABLE:
                        alert('❌ Position non disponible. Vérifiez votre connexion.');
                        break;
                      case error.TIMEOUT:
                        alert('❌ Délai d\'attente dépassé. Réessayez.');
                        break;
                      default:
                        alert('❌ Erreur inconnue lors de la géolocalisation.');
                        break;
                    }
                  },
                  {
                    enableHighAccuracy: true,
                    timeout: 10000,
                    maximumAge: 60000
                  }
                );
              } else {
                alert('❌ Géolocalisation non supportée par votre navigateur');
              }
            }}
          >
            📍 Ma position
          </button>
        </div>
      </div>

      {/* Liste des clients avec localisation */}
      {clients.length === 0 ? (
        <div className="tech-mobile-card">
          <div className="tech-mobile-empty-state">
            <div className="tech-mobile-empty-icon">🗺️</div>
            <h3>Aucun client trouvé</h3>
            <p>Aucun client n'est disponible pour la localisation.</p>
          </div>
        </div>
      ) : (
        clients.map(client => (
          <div key={client.idclient} className="tech-mobile-intervention-item">
            <div className="tech-mobile-intervention-header">
              <div className="tech-mobile-intervention-client">
                <strong>{client.nom} {client.prenom}</strong>
                <div style={{ fontSize: '14px', color: '#6b7280', marginTop: '2px' }}>
                  📍 {client.adresse}
                  {client.ville && `, ${client.ville}`}
                </div>
                {client.secteur_nom && (
                  <div style={{ fontSize: '12px', color: '#9ca3af', marginTop: '2px' }}>
                    🏘️ Secteur: {client.secteur_nom}
                  </div>
                )}
              </div>
              <div className="tech-mobile-intervention-badge">
                📍 Adresse
              </div>
            </div>

            <div className="tech-mobile-intervention-details">
              {client.tel && (
                <div className="tech-mobile-intervention-info">
                  <span>📞 {client.tel}</span>
                </div>
              )}
              {client.email && (
                <div className="tech-mobile-intervention-info">
                  <span>✉️ {client.email}</span>
                </div>
              )}
              <div className="tech-mobile-intervention-info">
                <span>🆔 ID Client: {client.idclient}</span>
              </div>
            </div>

            <div className="tech-mobile-intervention-actions">
              <button
                className="tech-mobile-action-btn start"
                onClick={() => openGoogleMaps(client)}
              >
                🗺️ Voir sur la carte
              </button>

            </div>
          </div>
        ))
      )}

      {/* Informations sur la géolocalisation */}
      <div className="tech-mobile-card">
        <div className="tech-mobile-card-header">
          <h2 className="tech-mobile-card-title">ℹ️ Informations</h2>
        </div>
        <div style={{ padding: '20px' }}>
          <p style={{ color: '#6b7280', fontSize: '14px', lineHeight: '1.5', margin: 0 }}>
            • Cliquez sur "Voir sur la carte" pour ouvrir Google Maps avec la position du client<br/>
            • Utilisez "Ma position" pour voir votre localisation actuelle
          </p>
        </div>
      </div>
    </div>
  );
};

export default MapPage;
