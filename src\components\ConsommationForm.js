import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, TouchableOpacity, Alert, Modal, StyleSheet, ScrollView } from 'react-native';

const ConsommationForm = ({ visible, client, onClose, onSubmit }) => {
  const [formData, setFormData] = useState({
    periode: '',
    consommationPre: '',
    consommationActuelle: '',
    jours: '',
    idtranch: 1, // Par défaut
    status: 'En cours'
  });
  const [loading, setLoading] = useState(false);
  const [contract, setContract] = useState(null);
  const [lastConsommation, setLastConsommation] = useState(null);

  useEffect(() => {
    if (visible && client) {
      fetchClientContract();
      fetchLastConsommation();
      // Générer la période actuelle (YYYY-MM)
      const now = new Date();
      const periode = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
      setFormData(prev => ({ ...prev, periode }));
    }
  }, [visible, client]);

  const fetchClientContract = async () => {
    try {
      const response = await fetch(`http://localhost:4000/api/client-contract/${client.idclient}`);
      const data = await response.json();
      if (data.success && data.contract) {
        setContract(data.contract);
      }
    } catch (error) {
      console.error('Erreur récupération contrat:', error);
    }
  };

  const fetchLastConsommation = async () => {
    try {
      const response = await fetch(`http://localhost:4000/api/clients/${client.idclient}/last-consommation`);
      const data = await response.json();
      if (data.success && data.data) {
        setLastConsommation(data.data);
        setFormData(prev => ({ 
          ...prev, 
          consommationPre: data.data.consommationactuelle.toString()
        }));
      }
    } catch (error) {
      console.error('Erreur récupération dernière consommation:', error);
    }
  };

  const calculateJours = () => {
    if (lastConsommation && formData.periode) {
      try {
        const currentPeriod = new Date(formData.periode + '-01');
        const lastPeriod = new Date(lastConsommation.periode + '-01');
        const diffTime = Math.abs(currentPeriod - lastPeriod);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        setFormData(prev => ({ ...prev, jours: diffDays.toString() }));
      } catch (error) {
        console.error('Erreur calcul jours:', error);
      }
    }
  };

  useEffect(() => {
    calculateJours();
  }, [formData.periode, lastConsommation]);

  const validateForm = () => {
    if (!formData.periode) {
      alert('Veuillez saisir la période');
      return false;
    }
    if (!formData.consommationActuelle) {
      alert('Veuillez saisir la consommation actuelle');
      return false;
    }
    if (parseInt(formData.consommationActuelle) <= parseInt(formData.consommationPre || 0)) {
      alert('La consommation actuelle doit être supérieure à la consommation précédente');
      return false;
    }
    if (!contract) {
      alert('Aucun contrat trouvé pour ce client');
      return false;
    }
    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      const consommationData = {
        consommationpre: parseInt(formData.consommationPre || 0),
        consommationactuelle: parseInt(formData.consommationActuelle),
        idcont: contract.idcontract,
        idtech: 1, // TODO: Récupérer l'ID du technicien connecté
        idtranch: formData.idtranch,
        jours: parseInt(formData.jours || 30),
        periode: formData.periode,
        status: formData.status
      };

      const response = await fetch('http://localhost:4000/api/consommations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(consommationData),
      });

      const result = await response.json();

      if (result.success) {
        alert('Consommation enregistrée avec succès !');
        onSubmit && onSubmit(result.data);
        onClose();
        resetForm();
      } else {
        alert(`Erreur: ${result.message}`);
      }
    } catch (error) {
      console.error('Erreur soumission:', error);
      alert(`Erreur de connexion: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      periode: '',
      consommationPre: '',
      consommationActuelle: '',
      jours: '',
      idtranch: 1,
      status: 'En cours'
    });
    setContract(null);
    setLastConsommation(null);
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={handleClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          <ScrollView style={styles.scrollContainer}>
            <View style={styles.header}>
              <Text style={styles.title}>💧 Saisie Consommation</Text>
              <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
                <Text style={styles.closeButtonText}>✕</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.clientInfo}>
              <Text style={styles.clientName}>{client?.nom} {client?.prenom}</Text>
              <Text style={styles.clientDetail}>📍 {client?.adresse}</Text>
              <Text style={styles.clientDetail}>🏙️ {client?.ville}</Text>
              {contract && (
                <Text style={styles.clientDetail}>🔧 Compteur: {contract.marquecompteur}</Text>
              )}
            </View>

            <View style={styles.form}>
              <View style={styles.formGroup}>
                <Text style={styles.label}>Période (YYYY-MM) *</Text>
                <TextInput
                  style={styles.input}
                  value={formData.periode}
                  onChangeText={(text) => setFormData(prev => ({ ...prev, periode: text }))}
                  placeholder="2024-01"
                  maxLength={7}
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.label}>Consommation Précédente (m³)</Text>
                <TextInput
                  style={[styles.input, styles.readOnlyInput]}
                  value={formData.consommationPre}
                  editable={false}
                  placeholder="Automatique"
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.label}>Consommation Actuelle (m³) *</Text>
                <TextInput
                  style={styles.input}
                  value={formData.consommationActuelle}
                  onChangeText={(text) => setFormData(prev => ({ ...prev, consommationActuelle: text }))}
                  placeholder="Saisir la consommation actuelle"
                  keyboardType="numeric"
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.label}>Nombre de jours</Text>
                <TextInput
                  style={[styles.input, styles.readOnlyInput]}
                  value={formData.jours}
                  editable={false}
                  placeholder="Calculé automatiquement"
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.label}>Statut</Text>
                <TextInput
                  style={styles.input}
                  value={formData.status}
                  onChangeText={(text) => setFormData(prev => ({ ...prev, status: text }))}
                  placeholder="En cours"
                />
              </View>
            </View>

            <View style={styles.buttonContainer}>
              <TouchableOpacity
                style={[styles.button, styles.cancelButton]}
                onPress={handleClose}
              >
                <Text style={styles.cancelButtonText}>Annuler</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.button, styles.submitButton, loading && styles.disabledButton]}
                onPress={handleSubmit}
                disabled={loading}
              >
                <Text style={styles.submitButtonText}>
                  {loading ? 'Enregistrement...' : 'Enregistrer'}
                </Text>
              </TouchableOpacity>
            </View>
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    backgroundColor: 'white',
    borderRadius: 10,
    width: '90%',
    maxHeight: '80%',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  scrollContainer: {
    maxHeight: '100%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    padding: 5,
  },
  closeButtonText: {
    fontSize: 20,
    color: '#666',
  },
  clientInfo: {
    padding: 20,
    backgroundColor: '#f8f9fa',
  },
  clientName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  clientDetail: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  form: {
    padding: 20,
  },
  formGroup: {
    marginBottom: 15,
  },
  label: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 5,
    padding: 10,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  readOnlyInput: {
    backgroundColor: '#f5f5f5',
    color: '#666',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 20,
    paddingTop: 0,
  },
  button: {
    flex: 1,
    padding: 15,
    borderRadius: 5,
    alignItems: 'center',
    marginHorizontal: 5,
  },
  cancelButton: {
    backgroundColor: '#f8f9fa',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  cancelButtonText: {
    color: '#666',
    fontWeight: 'bold',
  },
  submitButton: {
    backgroundColor: '#007AFF',
  },
  submitButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
});

export default ConsommationForm;
