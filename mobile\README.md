# 💧 AquaTrack Mobile - Application React Native

Application mobile native pour la gestion des consommations d'eau, développée avec React Native et Expo.

## 🚀 Fonctionnalités

### 🔐 Authentification
- Connexion sécurisée avec email/mot de passe
- Gestion des rôles (Technicien/Admin)
- Interface de connexion optimisée mobile

### 👥 Gestion des Clients
- Liste des clients avec recherche
- Informations détaillées de chaque client
- **Bouton Localisation** : Ouverture native de Google Maps
- Navigation fluide entre les écrans

### 💧 Saisie de Consommation
- Formulaire mobile optimisé
- Calcul automatique des jours
- Récupération de la consommation précédente
- Validation des données en temps réel

### 📱 Scanner QR
- Scanner QR natif avec caméra
- Identification automatique des clients
- Accès direct aux fonctionnalités client

### 📄 Consultation des Factures
- Liste des factures avec statuts
- Interface mobile adaptée
- Informations détaillées

## 🛠️ Technologies Utilisées

- **React Native** : Framework mobile natif
- **Expo** : Plateforme de développement
- **React Navigation** : Navigation native
- **Expo BarCode Scanner** : Scanner QR natif
- **React Native Linking** : Ouverture d'URLs natives

## 📱 APIs Mobiles Natives

### Localisation (Remplacement de window.open)
```javascript
import { Linking } from 'react-native';
await Linking.openURL('https://www.google.com/maps?q=lat,lng');
```

### Alertes (Remplacement de alert())
```javascript
import { Alert } from 'react-native';
Alert.alert('Titre', 'Message', [{ text: 'OK' }]);
```

### Navigation (Remplacement de React Router)
```javascript
import { useNavigation } from '@react-navigation/native';
navigation.navigate('ScreenName', { params });
```

## 🔧 Installation et Lancement

### Prérequis
- Node.js (v16 ou plus)
- Expo CLI : `npm install -g expo-cli`
- Application Expo Go sur votre téléphone

### Installation
```bash
cd mobile
npm install
```

### Lancement
```bash
# Démarrer le serveur de développement
npm start

# Ou directement sur Android
npm run android

# Ou directement sur iOS
npm run ios
```

### 📱 Test sur Téléphone
1. Installez l'app **Expo Go** sur votre téléphone
2. Scannez le QR code affiché dans le terminal
3. L'application se lance directement sur votre téléphone

## 🌐 Configuration Backend

Modifiez l'IP du serveur dans chaque écran :
```javascript
const API_BASE_URL = 'http://VOTRE_IP:4000'; // Remplacez par votre IP
```

## 📂 Structure du Projet

```
mobile/
├── App.js                          # Point d'entrée avec navigation
├── src/
│   └── screens/
│       ├── LoginScreen.js          # Écran de connexion
│       ├── TechnicianDashboard.js  # Dashboard technicien
│       ├── ClientsScreen.js        # Liste des clients + localisation
│       ├── ConsommationScreen.js   # Formulaire de consommation
│       ├── FacturesScreen.js       # Liste des factures
│       └── QRScannerScreen.js      # Scanner QR natif
├── package.json                    # Dépendances
├── app.json                        # Configuration Expo
└── babel.config.js                 # Configuration Babel
```

## 🎯 Différences avec la Version Web

| Fonctionnalité | Version Web | Version Mobile |
|----------------|-------------|----------------|
| Navigation | React Router | React Navigation |
| Alertes | `alert()` | `Alert.alert()` |
| Ouverture URLs | `window.open()` | `Linking.openURL()` |
| Scanner QR | Simulation | Caméra native |
| Interface | Navigateur | Application native |

## 🔐 Comptes de Test

- **Technicien** : `<EMAIL>` / `Tech123`
- **Admin** : `<EMAIL>` / `Admin123`

## 📱 Fonctionnalités Mobiles Spécifiques

### Bouton Localisation
- Ouverture native de Google Maps
- Gestion des permissions
- Interface d'alerte native

### Scanner QR
- Accès caméra natif
- Permissions automatiques
- Interface de scan optimisée

### Formulaire de Consommation
- Clavier numérique automatique
- Gestion du clavier virtuel
- Interface tactile optimisée

## 🚀 Déploiement

### Build Android (APK)
```bash
expo build:android
```

### Build iOS (IPA)
```bash
expo build:ios
```

### Publication sur les Stores
```bash
expo publish
```

## 📞 Support

Cette application mobile native remplace complètement la version web et offre une expérience utilisateur optimisée pour les appareils mobiles avec toutes les fonctionnalités natives attendues.
