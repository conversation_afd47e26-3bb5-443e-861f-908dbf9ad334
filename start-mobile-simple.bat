@echo off
echo ========================================
echo   AquaTrack Mobile - Démarrage Rapide
echo ========================================
echo.

REM Démarrer le backend
echo 🚀 Démarrage du backend...
start "Backend AquaTrack" cmd /k "cd server && node simple-working-server.js"

REM Attendre 3 secondes
timeout /t 3 /nobreak >nul

REM Démarrer React Native en mode Web
echo 🌐 Démarrage React Native en mode Web...
cd react-native
start "React Native Web" cmd /k "npx expo start --web --port 8081"

echo.
echo ✅ Projet démarré !
echo.
echo 📡 Backend: http://localhost:4000
echo 🌐 Frontend: http://localhost:8081
echo.
echo 🔑 Comptes de test:
echo   • <EMAIL> / Tech123
echo   • <EMAIL> / Admin123
echo.

pause
