import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { StatusBar } from 'expo-status-bar';

// Import des écrans
import LoginScreen from './src/screens/LoginScreen';
import TechnicianDashboard from './src/screens/TechnicianDashboard';
import ClientsScreen from './src/screens/ClientsScreen';
import ConsommationScreen from './src/screens/ConsommationScreen';
import FacturesScreen from './src/screens/FacturesScreen';
import QRScannerScreen from './src/screens/QRScannerScreen';

const Stack = createStackNavigator();

export default function App() {
  return (
    <NavigationContainer>
      <StatusBar style="auto" />
      <Stack.Navigator 
        initialRouteName="Login"
        screenOptions={{
          headerStyle: {
            backgroundColor: '#007AFF',
          },
          headerTintColor: '#fff',
          headerTitleStyle: {
            fontWeight: 'bold',
          },
        }}
      >
        <Stack.Screen 
          name="Login" 
          component={LoginScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen 
          name="TechnicianDashboard" 
          component={TechnicianDashboard}
          options={{ 
            title: 'Dashboard Technicien',
            headerLeft: null, // Empêche le retour
          }}
        />
        <Stack.Screen 
          name="Clients" 
          component={ClientsScreen}
          options={{ title: 'Mes Clients' }}
        />
        <Stack.Screen 
          name="Consommation" 
          component={ConsommationScreen}
          options={{ title: 'Consommation' }}
        />
        <Stack.Screen 
          name="Factures" 
          component={FacturesScreen}
          options={{ title: 'Factures' }}
        />
        <Stack.Screen 
          name="QRScanner" 
          component={QRScannerScreen}
          options={{ title: 'Scanner QR' }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
}
