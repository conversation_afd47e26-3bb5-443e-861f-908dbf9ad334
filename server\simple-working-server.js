const express = require('express');
const cors = require('cors');

const app = express();
app.use(express.json());
app.use(cors());

// Middleware de logging
app.use((req, res, next) => {
  console.log(`📥 ${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// Données de test
const testClients = [
  {
    idclient: 1,
    nom: '<PERSON><PERSON>',
    prenom: '<PERSON>',
    adresse: '123 Rue de la Paix',
    ville: 'Tu<PERSON>',
    tel: '71234567',
    email: '<EMAIL>',
    ids: 1,
    secteur_nom: 'Centre Ville'
  },
  {
    idclient: 2,
    nom: '<PERSON>',
    prenom: '<PERSON>',
    adresse: '456 Avenue Habib Bourguiba',
    ville: 'Sfax',
    tel: '74567890',
    email: '<EMAIL>',
    ids: 2,
    secteur_nom: 'Sfax Nord'
  }
];

const testSecteurs = [
  {
    ids: 1,
    nom: 'Centre Ville',
    latitude: 36.8065,
    longitude: 10.1815
  },
  {
    ids: 2,
    nom: 'Sfax Nord',
    latitude: 34.7406,
    longitude: 10.7603
  }
];

const testContracts = {
  1: [{
    idcontract: 1,
    codeqr: 'QR001',
    datecontract: '2024-01-15',
    marquecompteur: 'Sensus',
    numseriecompteur: 'SN123456',
    posx: '36.8075',
    posy: '10.1825'
  }],
  2: [{
    idcontract: 2,
    codeqr: 'QR002',
    datecontract: '2024-02-20',
    marquecompteur: 'Itron',
    numseriecompteur: 'IT789012',
    posx: '34.7416',
    posy: '10.7613'
  }]
};

// Données de test pour les utilisateurs
const testUsers = [
  {
    idtech: 1,
    nom: 'Technicien',
    prenom: 'Test',
    email: '<EMAIL>',
    password: 'Tech123',
    role: 'Tech'
  },
  {
    idtech: 2,
    nom: 'Admin',
    prenom: 'Test',
    email: '<EMAIL>',
    password: 'Admin123',
    role: 'Admin'
  }
];

// Route de test
app.get('/', (req, res) => {
  res.send('Serveur AquaTrack fonctionnel - Mode test avec données de démonstration');
});

// Route de login
app.post('/api/auth/login', (req, res) => {
  console.log('🔐 Tentative de connexion:', req.body);
  const { email, password } = req.body;

  if (!email || !password) {
    return res.status(400).json({
      success: false,
      message: "Email et mot de passe requis"
    });
  }

  // Rechercher l'utilisateur dans les données de test
  const user = testUsers.find(u => u.email === email && u.password === password);

  if (!user) {
    console.log('❌ Utilisateur non trouvé ou mot de passe incorrect');
    return res.status(401).json({
      success: false,
      message: "Email ou mot de passe incorrect"
    });
  }

  console.log(`✅ Connexion réussie pour: ${user.nom} ${user.prenom} (${user.role})`);

  res.json({
    success: true,
    message: "Connexion réussie",
    user: {
      idtech: user.idtech,
      nom: user.nom,
      prenom: user.prenom,
      email: user.email,
      role: user.role
    }
  });
});

// Route pour récupérer tous les clients
app.get('/api/clients', (req, res) => {
  console.log('📥 Récupération de tous les clients (données de test)');
  console.log(`✅ ${testClients.length} clients récupérés`);
  
  res.json({
    success: true,
    data: testClients,
    count: testClients.length,
    message: `${testClients.length} client(s) trouvé(s) (données de test)`
  });
});

// Route pour récupérer les contrats d'un client
app.get('/api/clients/:id/contracts', (req, res) => {
  const { id } = req.params;
  console.log(`🎯 GET /api/clients/${id}/contracts`);
  
  const clientId = parseInt(id);
  const client = testClients.find(c => c.idclient === clientId);
  
  if (!client) {
    console.log(`❌ Client ID ${id} non trouvé`);
    return res.status(404).json({
      success: false,
      message: `Client ID ${id} non trouvé`,
      client_id: clientId
    });
  }

  const contracts = testContracts[clientId] || [];
  console.log(`✅ Client trouvé: ${client.nom} ${client.prenom}`);
  console.log(`📊 ${contracts.length} contrat(s) trouvé(s)`);

  res.json({
    success: true,
    data: contracts,
    count: contracts.length,
    message: `${contracts.length} contrat(s) trouvé(s) pour le client ${id}`,
    client_id: clientId,
    client_name: `${client.nom} ${client.prenom}`
  });
});

// Route pour récupérer tous les secteurs
app.get('/api/secteurs', (req, res) => {
  console.log('📥 GET /api/secteurs - Récupération de tous les secteurs');
  console.log(`✅ ${testSecteurs.length} secteurs récupérés`);

  res.json({
    success: true,
    data: testSecteurs,
    count: testSecteurs.length,
    message: `${testSecteurs.length} secteur(s) trouvé(s) (données de test)`
  });
});

// Route pour récupérer un secteur par ID
app.get('/api/secteurs/:id', (req, res) => {
  const { id } = req.params;
  console.log(`📥 GET /api/secteurs/${id}`);

  const secteurId = parseInt(id);
  const secteur = testSecteurs.find(s => s.ids === secteurId);

  if (!secteur) {
    console.log(`❌ Secteur ID ${id} non trouvé`);
    return res.status(404).json({
      success: false,
      message: 'Secteur non trouvé'
    });
  }

  console.log(`✅ Secteur ${id} récupéré: ${secteur.nom}`);
  console.log(`📍 Coordonnées: ${secteur.latitude}, ${secteur.longitude}`);

  res.json({
    success: true,
    data: secteur,
    message: 'Secteur trouvé (données de test)'
  });
});

// Démarrage du serveur
const PORT = 4000;
app.listen(PORT, '0.0.0.0', () => {
  console.log('🔄 Démarrage du serveur AquaTrack...');
  console.log(`🚀 Serveur AquaTrack démarré sur http://localhost:${PORT}`);
  console.log('🗄️ Mode: Données de test');
  console.log('📡 Routes disponibles:');
  console.log('  - GET  / (test)');
  console.log('  - GET  /api/clients (tous les clients)');
  console.log('  - GET  /api/clients/:id/contracts (contrats du client) ⭐');
  console.log('  - GET  /api/secteurs/:id (secteur par ID avec coordonnées) 📍');
  console.log('✅ PRÊT À RECEVOIR LES REQUÊTES !');
});
