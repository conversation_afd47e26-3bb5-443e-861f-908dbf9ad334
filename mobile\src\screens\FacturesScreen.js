import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  SafeAreaView,
} from 'react-native';

const FacturesScreen = ({ navigation, route }) => {
  const [factures, setFactures] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  const API_BASE_URL = 'http://***********:4000'; // Remplacez par votre IP
  const user = route.params?.user;

  useEffect(() => {
    fetchFactures();
  }, []);

  const fetchFactures = async () => {
    try {
      setLoading(true);
      const response = await fetch(`${API_BASE_URL}/api/factures`);
      const data = await response.json();

      if (data.success && data.data) {
        setFactures(data.data);
        setError('');
      } else {
        setError('Aucune facture trouvée');
      }
    } catch (err) {
      console.error('Erreur lors de la récupération des factures:', err);
      setError('Erreur de connexion au serveur');
    } finally {
      setLoading(false);
    }
  };

  const renderFactureItem = ({ item }) => (
    <View style={styles.factureCard}>
      <View style={styles.factureHeader}>
        <Text style={styles.factureReference}>📄 {item.reference}</Text>
        <View style={[
          styles.statusBadge,
          item.status === 'payée' ? styles.statusPaid : styles.statusUnpaid
        ]}>
          <Text style={[
            styles.statusText,
            item.status === 'payée' ? styles.statusPaidText : styles.statusUnpaidText
          ]}>
            {item.status === 'payée' ? '✅ Payée' : '⏳ Non payée'}
          </Text>
        </View>
      </View>

      <View style={styles.factureInfo}>
        <Text style={styles.clientName}>
          👤 {item.client_nom} {item.client_prenom}
        </Text>
        <Text style={styles.factureDetail}>
          📅 Date: {new Date(item.date).toLocaleDateString('fr-FR')}
        </Text>
        <Text style={styles.factureDetail}>
          📊 Période: {item.periode}
        </Text>
        <Text style={styles.montant}>
          💰 Montant: {item.montant} DH
        </Text>
      </View>

      <TouchableOpacity
        style={styles.viewButton}
        onPress={() => Alert.alert('Info', 'Fonctionnalité de visualisation en développement')}
      >
        <Text style={styles.viewButtonText}>Voir Détails</Text>
      </TouchableOpacity>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Chargement des factures...</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>📄 Factures</Text>
        <Text style={styles.subtitle}>
          Consultation des factures
        </Text>
      </View>

      {error ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>❌ {error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={fetchFactures}>
            <Text style={styles.retryText}>Réessayer</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <FlatList
          data={factures}
          renderItem={renderFactureItem}
          keyExtractor={(item) => item.idfact.toString()}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
          refreshing={loading}
          onRefresh={fetchFactures}
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    backgroundColor: '#ffc107',
    padding: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
  },
  subtitle: {
    fontSize: 16,
    color: '#fff',
    opacity: 0.9,
  },
  listContainer: {
    padding: 15,
  },
  factureCard: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  factureHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  factureReference: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  statusBadge: {
    borderRadius: 15,
    paddingHorizontal: 10,
    paddingVertical: 5,
  },
  statusPaid: {
    backgroundColor: '#d4edda',
  },
  statusUnpaid: {
    backgroundColor: '#f8d7da',
  },
  statusText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  statusPaidText: {
    color: '#155724',
  },
  statusUnpaidText: {
    color: '#721c24',
  },
  factureInfo: {
    marginBottom: 15,
  },
  clientName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  factureDetail: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  montant: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#28a745',
    marginTop: 5,
  },
  viewButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    padding: 10,
    alignItems: 'center',
  },
  viewButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#dc3545',
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    padding: 12,
    paddingHorizontal: 20,
  },
  retryText: {
    color: '#fff',
    fontWeight: 'bold',
  },
});

export default FacturesScreen;
