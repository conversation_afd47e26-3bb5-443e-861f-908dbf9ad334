import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  TextInput,
  SafeAreaView,
  Linking,
} from 'react-native';

const ClientsScreen = ({ navigation, route }) => {
  const [clients, setClients] = useState([]);
  const [filteredClients, setFilteredClients] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchText, setSearchText] = useState('');

  const API_BASE_URL = 'http://***********:4000'; // Remplacez par votre IP
  const user = route.params?.user;

  useEffect(() => {
    fetchClients();
  }, []);

  useEffect(() => {
    filterClients();
  }, [clients, searchText]);

  const fetchClients = async () => {
    try {
      setLoading(true);
      const response = await fetch(`${API_BASE_URL}/api/clients`);
      const data = await response.json();

      if (data.success && data.data) {
        setClients(data.data);
        setError('');
      } else {
        setError('Aucun client trouvé');
      }
    } catch (err) {
      console.error('Erreur lors de la récupération des clients:', err);
      setError('Erreur de connexion au serveur');
    } finally {
      setLoading(false);
    }
  };

  const filterClients = () => {
    if (!searchText) {
      setFilteredClients(clients);
    } else {
      const filtered = clients.filter(client =>
        client.nom.toLowerCase().includes(searchText.toLowerCase()) ||
        client.prenom.toLowerCase().includes(searchText.toLowerCase()) ||
        client.ville.toLowerCase().includes(searchText.toLowerCase())
      );
      setFilteredClients(filtered);
    }
  };

  const handleConsommationPress = (client) => {
    console.log('Navigation vers consommation pour:', client.nom, client.prenom);
    navigation.navigate('Consommation', { client, user });
  };

  const handleLocationPress = async (client) => {
    try {
      console.log('🗺️ Localisation demandée pour:', client.nom, client.prenom);

      // Récupérer le contrat avec les informations du secteur
      const response = await fetch(`${API_BASE_URL}/api/client-contract/${client.idclient}`);
      const data = await response.json();

      if (!response.ok || !data.success || !data.contract) {
        Alert.alert(
          'Aucun Contrat',
          `Aucun contrat trouvé pour ${client.nom} ${client.prenom}.\n\nImpossible d'afficher la localisation.`
        );
        return;
      }

      const contract = data.contract;
      console.log('✅ Contrat trouvé:', contract);

      // Redirection vers Google Maps
      await redirectToGoogleMaps(client, contract);

    } catch (error) {
      console.error('❌ Erreur localisation:', error);
      Alert.alert('Erreur', `Erreur de connexion: ${error.message}`);
    }
  };

  const redirectToGoogleMaps = async (client, contract) => {
    try {
      // Étape 1: Ouvrir le secteur si disponible
      if (contract.secteur_latitude && contract.secteur_longitude) {
        const secteurUrl = `https://www.google.com/maps?q=${contract.secteur_latitude},${contract.secteur_longitude}&z=15`;
        
        Alert.alert(
          '🗺️ Localisation - Secteur',
          `Client: ${client.nom} ${client.prenom}\n\nSecteur: ${contract.secteur_nom || 'Non défini'}\n\nOuvrir la vue du secteur dans Google Maps ?`,
          [
            { text: 'Annuler', style: 'cancel' },
            {
              text: 'Ouvrir Secteur',
              onPress: async () => {
                await Linking.openURL(secteurUrl);
                
                // Proposer la position exacte après 2 secondes
                setTimeout(() => {
                  if (contract.posx && contract.posy) {
                    showExactLocationOption(client, contract);
                  } else {
                    Alert.alert(
                      'Secteur Ouvert',
                      `Vue du secteur ouverte dans Google Maps.\n\nPosition exacte du compteur non disponible.`
                    );
                  }
                }, 2000);
              }
            }
          ]
        );
      } else if (contract.posx && contract.posy) {
        // Seulement la position exacte disponible
        const exactUrl = `https://www.google.com/maps?q=${contract.posx},${contract.posy}&z=18`;
        await Linking.openURL(exactUrl);
        Alert.alert(
          'Position Exacte Ouverte',
          `Position du compteur ouverte dans Google Maps.\n\nInformations du secteur non disponibles.`
        );
      } else {
        Alert.alert(
          'Localisation Non Disponible',
          `Aucune coordonnée disponible pour ${client.nom} ${client.prenom}.\n\nVeuillez contacter l'administrateur.`
        );
      }
    } catch (error) {
      console.error('Erreur ouverture Google Maps:', error);
      Alert.alert('Erreur', 'Impossible d\'ouvrir Google Maps');
    }
  };

  const showExactLocationOption = (client, contract) => {
    Alert.alert(
      '🏠 Position Exacte',
      `Voulez-vous maintenant voir la position exacte du compteur de ${client.nom} ${client.prenom} ?`,
      [
        { text: 'Non', style: 'cancel' },
        {
          text: 'Ouvrir Position Exacte',
          onPress: async () => {
            const exactUrl = `https://www.google.com/maps?q=${contract.posx},${contract.posy}&z=18`;
            await Linking.openURL(exactUrl);
            Alert.alert(
              'Position Exacte Ouverte',
              `Position exacte du compteur ouverte dans Google Maps.`
            );
          }
        }
      ]
    );
  };

  const renderClientItem = ({ item }) => (
    <View style={styles.clientCard}>
      <View style={styles.clientInfo}>
        <Text style={styles.clientName}>{item.nom} {item.prenom}</Text>
        <Text style={styles.clientDetail}>📍 {item.adresse}</Text>
        <Text style={styles.clientDetail}>🏙️ {item.ville}</Text>
        <Text style={styles.clientDetail}>📞 {item.tel}</Text>
        <Text style={styles.clientDetail}>📧 {item.email}</Text>
        {item.statut && (
          <View style={styles.statusContainer}>
            <Text style={[
              styles.statusText,
              item.statut === 'Actif' ? styles.statusActive : styles.statusInactive
            ]}>
              {item.statut}
            </Text>
          </View>
        )}
      </View>

      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => handleConsommationPress(item)}
        >
          <Text style={styles.buttonIcon}>💧</Text>
          <Text style={styles.buttonText}>Consommation</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, styles.locationButton]}
          onPress={() => handleLocationPress(item)}
        >
          <Text style={styles.buttonIcon}>📍</Text>
          <Text style={styles.buttonText}>Localisation</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Chargement des clients...</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>👥 Mes Clients</Text>
        <Text style={styles.subtitle}>
          Bonjour {user?.nom} {user?.prenom}
        </Text>
      </View>

      <View style={styles.searchContainer}>
        <TextInput
          style={styles.searchInput}
          placeholder="🔍 Rechercher un client..."
          value={searchText}
          onChangeText={setSearchText}
        />
      </View>

      {error ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>❌ {error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={fetchClients}>
            <Text style={styles.retryText}>Réessayer</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <FlatList
          data={filteredClients}
          renderItem={renderClientItem}
          keyExtractor={(item) => item.idclient.toString()}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
          refreshing={loading}
          onRefresh={fetchClients}
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    backgroundColor: '#007AFF',
    padding: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
  },
  subtitle: {
    fontSize: 16,
    color: '#fff',
    opacity: 0.9,
  },
  searchContainer: {
    padding: 15,
  },
  searchInput: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 15,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  listContainer: {
    padding: 15,
  },
  clientCard: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  clientInfo: {
    marginBottom: 15,
  },
  clientName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  clientDetail: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  statusContainer: {
    marginTop: 5,
  },
  statusText: {
    fontSize: 12,
    fontWeight: 'bold',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 10,
    alignSelf: 'flex-start',
  },
  statusActive: {
    backgroundColor: '#d4edda',
    color: '#155724',
  },
  statusInactive: {
    backgroundColor: '#f8d7da',
    color: '#721c24',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flex: 1,
    backgroundColor: '#007AFF',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    marginHorizontal: 5,
  },
  locationButton: {
    backgroundColor: '#28a745',
  },
  buttonIcon: {
    fontSize: 20,
    marginBottom: 5,
  },
  buttonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#dc3545',
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    padding: 12,
    paddingHorizontal: 20,
  },
  retryText: {
    color: '#fff',
    fontWeight: 'bold',
  },
});

export default ClientsScreen;
