const express = require('express');
const { Pool } = require('pg');
const router = express.Router();

// Configuration de la base de données PostgreSQL "Facturation"
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facturation', 
  password: '123456', 
  port: 5432,
});

// Route pour récupérer tous les clients
router.get('/clients', async (req, res) => {
  try {
    console.log('🔍 Récupération de tous les clients...');
    console.log('📊 Configuration DB:', {
      user: 'postgres',
      host: 'localhost',
      database: 'Facturation',
      port: 5432
    });

    // Test de connexion
    const testQuery = 'SELECT NOW() as current_time';
    const testResult = await pool.query(testQuery);
    console.log('✅ Connexion DB réussie:', testResult.rows[0]);

    // Vérifier si la table client existe
    const tableExists = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'client'
      );
    `);
    console.log('📋 Table client existe:', tableExists.rows[0].exists);

    if (!tableExists.rows[0].exists) {
      console.log('❌ Table client n\'existe pas!');
      return res.status(404).json({
        success: false,
        message: 'Table client non trouvée',
        clients: [],
        total: 0
      });
    }

    const clientsQuery = `
      SELECT
        idclient,
        nom,
        prenom,
        adresse,
        ville,
        tel,
        email,
        statut
      FROM client
      ORDER BY nom, prenom
    `;

    console.log('📝 Exécution de la requête:', clientsQuery);
    const result = await pool.query(clientsQuery);

    console.log(`✅ ${result.rows.length} clients trouvés`);

    res.json({
      success: true,
      data: result.rows.map(client => ({
        idclient: client.idclient,
        nom: client.nom,
        prenom: client.prenom,
        adresse: client.adresse,
        ville: client.ville,
        tel: client.tel,
        email: client.email,
        statut: client.statut
      })),
      clients: result.rows.map(client => ({
        idclient: client.idclient,
        nom: client.nom,
        prenom: client.prenom,
        adresse: client.adresse,
        ville: client.ville,
        tel: client.tel,
        email: client.email,
        statut: client.statut
      })),
      total: result.rows.length,
      count: result.rows.length
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des clients:', error);
    console.error('📋 Détails de l\'erreur:', {
      message: error.message,
      code: error.code,
      detail: error.detail,
      hint: error.hint,
      position: error.position,
      routine: error.routine
    });

    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des clients',
      error: error.message,
      code: error.code,
      detail: error.detail
    });
  }
});

// Route pour récupérer un client par ID
router.get('/clients/:id', async (req, res) => {
  const { id } = req.params;
  
  try {
    console.log(`🔍 Récupération du client ID: ${id}`);

    const clientQuery = `
      SELECT 
        c.idClient,
        c.nom,
        c.prenom,
        c.adresse,
        c.ville,
        c.tel,
        c.email,
        s.nom as secteur_nom
      FROM Client c
      LEFT JOIN Secteur s ON c.idS = s.idS
      WHERE c.idClient = $1
    `;

    const result = await pool.query(clientQuery, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Client non trouvé'
      });
    }

    const client = result.rows[0];

    res.json({
      success: true,
      client: {
        idClient: client.idclient,
        nom: client.nom,
        prenom: client.prenom,
        adresse: client.adresse,
        ville: client.ville,
        tel: client.tel,
        email: client.email,
        secteur_nom: client.secteur_nom
      }
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération du client:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération du client',
      error: error.message
    });
  }
});

// Route pour récupérer les contrats d'un client
router.get('/clients/:id/contracts', async (req, res) => {
  const { id } = req.params;
  
  try {
    console.log(`🔍 Récupération des contrats pour le client ID: ${id}`);

    const contractsQuery = `
      SELECT 
        cont.idContract,
        cont.codeQr,
        cont.dateContract,
        cont.marqueCompteur,
        cont.numSerieCompteur,
        cont.posX,
        cont.posY
      FROM Contract cont
      WHERE cont.idClient = $1
      ORDER BY cont.dateContract DESC
    `;

    const result = await pool.query(contractsQuery, [id]);

    console.log(`✅ ${result.rows.length} contrat(s) trouvé(s) pour le client ${id}`);

    res.json({
      success: true,
      count: result.rows.length,
      data: result.rows.map(contract => ({
        idContract: contract.idcontract,
        codeQr: contract.codeqr,
        dateContract: contract.datecontract,
        marqueCompteur: contract.marquecompteur,
        numSerieCompteur: contract.numseriecompteur,
        posX: contract.posx,
        posY: contract.posy
      }))
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des contrats:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des contrats',
      error: error.message
    });
  }
});

// Route pour récupérer tous les secteurs
router.get('/secteurs', async (req, res) => {
  try {
    console.log('🔍 Récupération de tous les secteurs...');

    const secteursQuery = `
      SELECT idS, nom
      FROM Secteur
      ORDER BY nom
    `;

    const result = await pool.query(secteursQuery);

    console.log(`✅ ${result.rows.length} secteur(s) trouvé(s)`);

    res.json({
      success: true,
      count: result.rows.length,
      data: result.rows.map(secteur => ({
        idS: secteur.ids,
        nom: secteur.nom
      }))
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des secteurs:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des secteurs',
      error: error.message
    });
  }
});

// Route pour récupérer le contrat et secteur d'un client pour la localisation
router.get('/client-contract/:id', async (req, res) => {
  const { id } = req.params;

  try {
    console.log(`🔍 Récupération du contrat et secteur pour le client ID: ${id}`);

    const contractQuery = `
      SELECT
        c.idcontract,
        c.codeqr,
        c.datecontract,
        c.idclient,
        c.idsecteur,
        c.marquecompteur,
        c.numseriecompteur,
        c.posx,
        c.posy,
        c.datedebut,
        c.datefin,
        s.ids as secteur_id,
        s.nom as secteur_nom,
        s.latitude as secteur_latitude,
        s.longitude as secteur_longitude
      FROM contract c
      LEFT JOIN secteur s ON c.idsecteur = s.ids
      WHERE c.idclient = $1
      ORDER BY c.datecontract DESC
      LIMIT 1
    `;

    const result = await pool.query(contractQuery, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Aucun contrat trouvé pour ce client'
      });
    }

    const contract = result.rows[0];

    console.log(`✅ Contrat trouvé pour le client ${id}:`, {
      idcontract: contract.idcontract,
      secteur: contract.secteur_nom,
      position: `${contract.posx}, ${contract.posy}`
    });

    res.json({
      success: true,
      contract: {
        idcontract: contract.idcontract,
        codeqr: contract.codeqr,
        datecontract: contract.datecontract,
        idclient: contract.idclient,
        idsecteur: contract.idsecteur,
        marquecompteur: contract.marquecompteur,
        numseriecompteur: contract.numseriecompteur,
        posx: contract.posx,
        posy: contract.posy,
        datedebut: contract.datedebut,
        datefin: contract.datefin,
        secteur_id: contract.secteur_id,
        secteur_nom: contract.secteur_nom,
        secteur_latitude: contract.secteur_latitude,
        secteur_longitude: contract.secteur_longitude
      }
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération du contrat:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération du contrat',
      error: error.message
    });
  }
});

// Route pour tester les données contract et secteur
router.get('/test-contract-data', async (req, res) => {
  try {
    // Test des tables
    const contractsResult = await pool.query('SELECT COUNT(*) as count FROM contract');
    const secteursResult = await pool.query('SELECT COUNT(*) as count FROM secteur');
    const clientsWithContractsResult = await pool.query(`
      SELECT DISTINCT c.idclient, cl.nom, cl.prenom
      FROM contract c
      JOIN client cl ON c.idclient = cl.idclient
      LIMIT 5
    `);

    res.json({
      success: true,
      contracts_count: contractsResult.rows[0].count,
      secteurs_count: secteursResult.rows[0].count,
      clients_with_contracts: clientsWithContractsResult.rows
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erreur lors du test des données',
      error: error.message
    });
  }
});

// Route pour tester la connexion à la base de données
router.get('/test-db', async (req, res) => {
  try {
    const result = await pool.query('SELECT NOW()');
    res.json({
      success: true,
      message: 'Connexion à la base de données réussie',
      timestamp: result.rows[0].now
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erreur de connexion à la base de données',
      error: error.message
    });
  }
});

module.exports = router;
