import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  SafeAreaView,
  Dimensions,
} from 'react-native';
import { BarCodeScanner } from 'expo-barcode-scanner';

const QRScannerScreen = ({ navigation, route }) => {
  const [hasPermission, setHasPermission] = useState(null);
  const [scanned, setScanned] = useState(false);
  const [scanning, setScanning] = useState(false);

  const API_BASE_URL = 'http://***********:4000'; // Remplacez par votre IP
  const user = route.params?.user;

  useEffect(() => {
    getBarCodeScannerPermissions();
  }, []);

  const getBarCodeScannerPermissions = async () => {
    const { status } = await BarCodeScanner.requestPermissionsAsync();
    setHasPermission(status === 'granted');
  };

  const handleBarCodeScanned = async ({ type, data }) => {
    if (scanned) return;
    
    setScanned(true);
    setScanning(false);
    
    console.log('QR Code scanné:', data);
    
    try {
      // Rechercher le client par code QR
      const response = await fetch(`${API_BASE_URL}/api/scanner/qr/${data}`);
      const result = await response.json();

      if (result.success && result.client) {
        const client = result.client;
        
        Alert.alert(
          '✅ Client Trouvé',
          `${client.nom} ${client.prenom}\n📍 ${client.adresse}\n🏙️ ${client.ville}`,
          [
            { text: 'Fermer', style: 'cancel' },
            {
              text: 'Voir Consommation',
              onPress: () => {
                navigation.navigate('Consommation', { client, user });
              }
            },
            {
              text: 'Voir Profil',
              onPress: () => {
                navigation.navigate('Clients', { user, selectedClient: client });
              }
            }
          ]
        );
      } else {
        Alert.alert(
          '❌ Client Non Trouvé',
          'Ce code QR ne correspond à aucun client dans la base de données.',
          [
            { text: 'OK', onPress: () => setScanned(false) }
          ]
        );
      }
    } catch (error) {
      console.error('Erreur lors de la recherche du client:', error);
      Alert.alert(
        'Erreur',
        'Impossible de vérifier le code QR. Vérifiez votre connexion.',
        [
          { text: 'OK', onPress: () => setScanned(false) }
        ]
      );
    }
  };

  const startScanning = () => {
    setScanned(false);
    setScanning(true);
  };

  const stopScanning = () => {
    setScanning(false);
    setScanned(false);
  };

  if (hasPermission === null) {
    return (
      <View style={styles.permissionContainer}>
        <Text style={styles.permissionText}>Demande d'autorisation caméra...</Text>
      </View>
    );
  }

  if (hasPermission === false) {
    return (
      <View style={styles.permissionContainer}>
        <Text style={styles.permissionText}>❌ Accès à la caméra refusé</Text>
        <Text style={styles.permissionSubText}>
          Veuillez autoriser l'accès à la caméra dans les paramètres de l'application
        </Text>
        <TouchableOpacity 
          style={styles.settingsButton}
          onPress={getBarCodeScannerPermissions}
        >
          <Text style={styles.settingsButtonText}>Réessayer</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>📱 Scanner QR</Text>
        <Text style={styles.subtitle}>
          Scannez le code QR d'un client
        </Text>
      </View>

      {scanning ? (
        <View style={styles.scannerContainer}>
          <BarCodeScanner
            onBarCodeScanned={scanned ? undefined : handleBarCodeScanned}
            style={styles.scanner}
          />
          
          <View style={styles.scannerOverlay}>
            <View style={styles.scannerFrame} />
            <Text style={styles.scannerText}>
              Placez le code QR dans le cadre
            </Text>
          </View>

          <View style={styles.scannerControls}>
            <TouchableOpacity
              style={styles.stopButton}
              onPress={stopScanning}
            >
              <Text style={styles.stopButtonText}>Arrêter</Text>
            </TouchableOpacity>
          </View>
        </View>
      ) : (
        <View style={styles.instructionsContainer}>
          <View style={styles.instructionsContent}>
            <Text style={styles.instructionsIcon}>📱</Text>
            <Text style={styles.instructionsTitle}>Scanner un Code QR</Text>
            <Text style={styles.instructionsText}>
              Appuyez sur "Démarrer le scan" pour ouvrir la caméra et scanner le code QR d'un client.
            </Text>
            
            <View style={styles.featuresContainer}>
              <Text style={styles.featuresTitle}>Fonctionnalités :</Text>
              <Text style={styles.featureItem}>• 🔍 Identification automatique du client</Text>
              <Text style={styles.featureItem}>• 💧 Accès direct à la consommation</Text>
              <Text style={styles.featureItem}>• 👤 Consultation du profil client</Text>
            </View>

            <TouchableOpacity
              style={styles.startButton}
              onPress={startScanning}
            >
              <Text style={styles.startButtonText}>📷 Démarrer le scan</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </SafeAreaView>
  );
};

const { width, height } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  permissionText: {
    fontSize: 18,
    textAlign: 'center',
    marginBottom: 10,
  },
  permissionSubText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
  },
  settingsButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    padding: 12,
    paddingHorizontal: 20,
  },
  settingsButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  header: {
    backgroundColor: '#28a745',
    padding: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
  },
  subtitle: {
    fontSize: 16,
    color: '#fff',
    opacity: 0.9,
  },
  scannerContainer: {
    flex: 1,
    position: 'relative',
  },
  scanner: {
    flex: 1,
  },
  scannerOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scannerFrame: {
    width: 250,
    height: 250,
    borderWidth: 2,
    borderColor: '#fff',
    borderRadius: 10,
    backgroundColor: 'transparent',
  },
  scannerText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 20,
    textAlign: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
    padding: 10,
    borderRadius: 5,
  },
  scannerControls: {
    position: 'absolute',
    bottom: 50,
    left: 0,
    right: 0,
    alignItems: 'center',
  },
  stopButton: {
    backgroundColor: '#dc3545',
    borderRadius: 25,
    paddingVertical: 15,
    paddingHorizontal: 30,
  },
  stopButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  instructionsContainer: {
    flex: 1,
    justifyContent: 'center',
    padding: 20,
  },
  instructionsContent: {
    backgroundColor: '#fff',
    borderRadius: 15,
    padding: 30,
    alignItems: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
  },
  instructionsIcon: {
    fontSize: 64,
    marginBottom: 20,
  },
  instructionsTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
    textAlign: 'center',
  },
  instructionsText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 25,
  },
  featuresContainer: {
    alignSelf: 'stretch',
    marginBottom: 30,
  },
  featuresTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  featureItem: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  startButton: {
    backgroundColor: '#28a745',
    borderRadius: 25,
    paddingVertical: 15,
    paddingHorizontal: 30,
  },
  startButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
});

export default QRScannerScreen;
